[project]
name = "job-search-ai-assistant"
version = "0.1.0"
description = "A comprehensive job search aggregator built with Python 3.13 and FastAPI that integrates multiple job platforms to streamline the job hunting process."
authors = [{ name = "RYZHAIEV SERHII", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.13"
license = { text = "MIT" }
keywords = ["python"]
classifiers = [
  "Intended Audience :: Developers",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "fastapi>=0.115.12",
    "uvicorn>=0.34.2",
    "pydantic>=2.11.5",
]

[project.urls]
Homepage = "https://RYZHAIEV-SERHII.github.io/job-search-ai-assistant/"
Repository = "https://github.com/RYZHAIEV-SERHII/job-search-ai-assistant"
Documentation = "https://RYZHAIEV-SERHII.github.io/job-search-ai-assistant/"

[dependency-groups]
dev = [
  "pytest>=8.3.5",
  "pre-commit>=4.2.0",
  "tox-uv>=1.25.0",
  "deptry>=0.23.0",
  "mypy>=1.15.0",
  "pytest-cov>=6.1.1",
  "ruff>=0.11.11",
  "mkdocs>=1.6.1",
  "mkdocs-material>=9.6.14",
  "mkdocs-github-admonitions-plugin>=0.0.3",
  "mkdocstrings[python]>=0.29.1",
  "python-semantic-release>=10.0.0",
]

# Build system configuration
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

# Configuration for wheel package building via hatch
[tool.hatch.build.targets.wheel]
packages = ["src"]

# Version configuration via hatch
[tool.hatch.version]
path = "pyproject.toml:version"

# Configuration for mypy
[tool.mypy]
files = ["src"]
disallow_untyped_defs = true
disallow_any_unimported = true
no_implicit_optional = true
check_untyped_defs = true
warn_return_any = true
warn_unused_ignores = true
show_error_codes = true

# Pytest configuration for testing
[tool.pytest.ini_options]
minversion = "8.0"
addopts = "-ra -q --strict-markers --cov-report=term-missing"  # Pytest run options
testpaths = ["tests"]
pythonpath = ["."]
markers = [
    "unit: mark a test as a unit test",
    "integration: mark a test as an integration test",
]

# Configuration for deptry
[tool.deptry]
ignore = ["DEP003", "DEP004"]

# Configuration for ruff
[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Assume Python 3.9
target-version = "py39"
line-length = 120
indent-width = 4
fix = true

[tool.ruff.lint]
select = [
  # flake8-2020
  "YTT",
  # flake8-bandit
  "S",
  # flake8-bugbear
  "B",
  # flake8-builtins
  "A",
  # flake8-comprehensions
  "C4",
  # flake8-debugger
  "T10",
  # flake8-simplify
  "SIM",
  # isort
  "I",
  # mccabe
  "C90",
  # pycodestyle
  "E",
  "W",
  # pyflakes
  "F",
  # pygrep-hooks
  "PGH",
  # pyupgrade
  "UP",
  # ruff
  "RUF",
  # tryceratops
  "TRY",
  # Pylint-equivalent rules
  "PLC",
  "PLE",
  "PLR",
  "PLW",
]
ignore = [
  # LineTooLong
  "E501",
  # DoNotAssignLambda
  "E731",
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"] # Ignore "S101: Use with is preferred to if" in tests

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = true

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"


# Code coverage collection settings
[tool.coverage.run]
source = ["src"]                   # Packages to measure coverage

# Coverage report configuration
[tool.coverage.report]
skip_empty = true
exclude_lines = [                  # Line patterns to exclude from analysis
    "pragma: no cover",            # Lines with 'no cover' directive
    "def __repr__",                # Repr methods
    "if self.debug:",              # Debugging code
    "raise NotImplementedError",   # Stubs for unimplemented methods
    "if __name__ == .__main__.:",  # Blocks executed on direct run
    "pass",                        # Empty blocks
    "raise ImportError",           # Import error handling
]
ignore_errors = true               # Ignore errors during report generation
omit = [                           # Files to exclude from analysis
    "tests/*",                     # Tests
    "setup.py",                    # Setup file
    "config.py",                   # Configuration file
]


# Automated version management configuration
[tool.semantic_release]
version_variables = [              # Paths to version variables that need to be updated
    "pyproject.toml:version"       # Updates version in pyproject.toml
]
branch = false                     # Don't create a separate branch for release
upload_to_pypi = false             # Don't upload package to PyPI
build_command = "pip install uv && uv build"  # Command for building the project
dist_path = "dist/"                # Path to the build artifacts directory
remove_dist = true                 # Remove dist directory before building
major_on_zero = false              # Don't increment major version at 0.x.x
tag_format = "{version}"           # Git tag format
commit_message = "chore(release): bump version to {version}"  # Commit message format
changelog_file = "CHANGELOG.md"    # File for recording changes between versions

# Commit message parsing configuration
[tool.semantic_release.commit_parser_options]
allowed_tags = [                   # Allowed types of changes in commit messages
    "feat",                        # New features
    "fix",                         # Bug fixes
    "perf",                        # Performance improvements
    "refactor",                    # Code refactoring
    "style",                       # Style changes
    "docs",                        # Documentation
    "test",                        # Tests
    "ci",                          # CI integration
    "build",                       # Build system
    "chore"                        # Other changes
]
minor_tags = ["feat"]              # Commit types that trigger minor version increment
patch_tags = ["fix", "perf"]       # Commit types that trigger patch version increment

# Main branch configuration
[tool.semantic_release.branches.main]
match = "main"                     # Pattern for identifying main branch
prerelease = false                 # Don't consider releases on this branch as prereleases

# Release candidate branches configuration
[tool.semantic_release.branches.rc]
match = "rc/*"                     # Pattern for identifying RC branches
prerelease = true                  # Consider releases on these branches as prereleases
prerelease_token = "rc"            # Prerelease token (rc)

# Beta branches configuration
[tool.semantic_release.branches.beta]
match = "beta/*"                   # Pattern for identifying beta branches
prerelease = true                  # Consider releases on these branches as prereleases
prerelease_token = "b"             # Prerelease token (b)

# Alpha branches configuration
[tool.semantic_release.branches.alpha]
match = "alpha/*"                  # Pattern for identifying alpha branches
prerelease = true                  # Consider releases on these branches as prereleases
prerelease_token = "a"             # Prerelease token (a)

# Prerelease tag formats according to PEP 440
[tool.semantic_release.prerelease_tag_format]
alpha = "{version}a{prerelease_offset}"         # Format for alpha releases (e.g. 0.6.12a1)
beta = "{version}b{prerelease_offset}"          # Format for beta releases (e.g. 0.6.12b1)
rc = "{version}rc{prerelease_offset}"           # Format for RC releases (e.g. 0.6.12rc1)

# CHANGELOG file generation configuration
[tool.semantic_release.changelog]
changelog_sections = [             # Sections for changelog and their display
    ["feat", "Features"],          # Feature commits -> Features section
    ["fix", "Bug Fixes"],          # Fix commits -> Bug Fixes section
    ["perf", "Performance"],       # Performance commits -> Performance section
    ["refactor", "Code Refactoring"], # Refactoring commits -> Code Refactoring section
    ["docs", "Documentation"],     # Documentation commits -> Documentation section
]
